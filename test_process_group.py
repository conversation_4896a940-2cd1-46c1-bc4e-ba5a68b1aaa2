#!/usr/bin/env python3
"""
Test script to verify process group management works correctly.
This simulates the Wappalyzer scenario where a parent process spawns child processes.
"""

import subprocess
import os
import signal
import time
import sys

def test_process_group_management():
    """Test that process groups are properly created and killed"""
    
    # Create a test script that spawns a child process
    test_script = """
import subprocess
import time
import sys

# Spawn a child process that will sleep
child = subprocess.Popen(['sleep', '30'])
print(f"Parent PID: {os.getpid()}")
print(f"Child PID: {child.pid}")
sys.stdout.flush()

# Keep parent alive
time.sleep(30)
"""
    
    # Write test script to temporary file
    with open('/tmp/test_parent.py', 'w') as f:
        f.write(test_script)
    
    print("Testing process group management...")
    
    # Start the test process with process group
    process = subprocess.Popen(
        ['python3', '/tmp/test_parent.py'],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        preexec_fn=os.setsid,  # Create new process group
    )
    
    print(f"Test process PID: {process.pid}")
    print(f"Test process group ID: {os.getpgid(process.pid)}")
    
    # Let it run for a bit
    time.sleep(2)
    
    # Check if process is running
    if process.poll() is None:
        print("Process is running, attempting to kill process group...")
        
        try:
            # Kill the entire process group
            os.killpg(os.getpgid(process.pid), signal.SIGTERM)
            print("Sent SIGTERM to process group")
            
            # Wait for termination
            for i in range(5):
                if process.poll() is not None:
                    print(f"Process terminated after {i} seconds")
                    break
                time.sleep(1)
            
            # Force kill if still running
            if process.poll() is None:
                os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                print("Sent SIGKILL to process group")
                process.wait()
                
        except ProcessLookupError:
            print("Process group already terminated")
        except Exception as e:
            print(f"Error killing process group: {e}")
    
    # Clean up
    try:
        os.unlink('/tmp/test_parent.py')
    except:
        pass
    
    print("Test completed")

if __name__ == "__main__":
    test_process_group_management()
